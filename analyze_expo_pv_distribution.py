#!/usr/bin/env python3
"""
脚本用于统计CSV文件中不同版本的曝光量分布
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import argparse

def load_data(csv_file):
    """加载CSV数据"""
    try:
        # 读取CSV时处理空值标记
        df = pd.read_csv(csv_file, na_values=['\\N', 'NULL', 'null', '', 'NaN'])
        print(f"成功加载数据，共 {len(df)} 行")

        # 确保曝光量列为数值类型
        expo_columns = ['expo_pv_15', 'expo_pv_16', 'expo_pv_19']
        for col in expo_columns:
            if col in df.columns:
                # 转换为数值类型，无法转换的设为NaN
                df[col] = pd.to_numeric(df[col], errors='coerce')
                print(f"列 {col}: 有效数值 {df[col].notna().sum()} 个，空值 {df[col].isna().sum()} 个")

        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def analyze_expo_pv_distribution(df):
    """分析曝光量分布"""
    
    # 提取三个版本的曝光量数据
    expo_pv_15 = df['expo_pv_15'].dropna()
    expo_pv_16 = df['expo_pv_16'].dropna()
    expo_pv_19 = df['expo_pv_19'].dropna()
    
    print("=" * 60)
    print("曝光量分布统计报告")
    print("=" * 60)
    
    # 基本统计信息
    versions = {
        'Version 15': expo_pv_15,
        'Version 16': expo_pv_16,
        'Version 19': expo_pv_19
    }
    
    for version_name, data in versions.items():
        print(f"\n{version_name} 曝光量统计:")
        print(f"  总数: {len(data)}")
        print(f"  均值: {data.mean():.2f}")
        print(f"  中位数: {data.median():.2f}")
        print(f"  标准差: {data.std():.2f}")
        print(f"  最小值: {data.min()}")
        print(f"  最大值: {data.max()}")
        print(f"  25%分位数: {data.quantile(0.25):.2f}")
        print(f"  75%分位数: {data.quantile(0.75):.2f}")
    
    # 分布区间统计
    print("\n" + "=" * 60)
    print("曝光量区间分布")
    print("=" * 60)
    
    # 定义区间
    bins = [0, 1, 5, 10, 20, 50, 100, float('inf')]
    labels = ['0', '1-4', '5-9', '10-19', '20-49', '50-99', '100+']
    
    for version_name, data in versions.items():
        print(f"\n{version_name} 区间分布:")
        binned = pd.cut(data, bins=bins, labels=labels, right=False)
        counts = binned.value_counts().sort_index()
        percentages = (counts / len(data) * 100).round(2)
        
        for label, count, pct in zip(labels, counts, percentages):
            print(f"  {label:>6}: {count:>6} 条 ({pct:>6.2f}%)")
    
    # 详细数值分布（前20个最常见的值）
    print("\n" + "=" * 60)
    print("最常见的曝光量数值 (前20个)")
    print("=" * 60)
    
    for version_name, data in versions.items():
        print(f"\n{version_name} 最常见数值:")
        value_counts = data.value_counts().head(20)
        for value, count in value_counts.items():
            percentage = (count / len(data) * 100)
            print(f"  {value:>6}: {count:>6} 条 ({percentage:>6.2f}%)")
    
    return versions

def create_visualizations(versions, output_dir="./"):
    """创建可视化图表"""
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 1. 箱线图比较
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 箱线图
    data_for_box = [versions['Version 15'], versions['Version 16'], versions['Version 19']]
    axes[0, 0].boxplot(data_for_box, labels=['Version 15', 'Version 16', 'Version 19'])
    axes[0, 0].set_title('曝光量分布箱线图')
    axes[0, 0].set_ylabel('曝光量')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 直方图
    for i, (version_name, data) in enumerate(versions.items()):
        axes[0, 1].hist(data, bins=30, alpha=0.7, label=version_name, density=True)
    axes[0, 1].set_title('曝光量分布直方图')
    axes[0, 1].set_xlabel('曝光量')
    axes[0, 1].set_ylabel('密度')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 累积分布图
    for version_name, data in versions.items():
        sorted_data = np.sort(data)
        y = np.arange(1, len(sorted_data) + 1) / len(sorted_data)
        axes[1, 0].plot(sorted_data, y, label=version_name, linewidth=2)
    axes[1, 0].set_title('累积分布函数')
    axes[1, 0].set_xlabel('曝光量')
    axes[1, 0].set_ylabel('累积概率')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 小提琴图
    data_for_violin = [versions['Version 15'], versions['Version 16'], versions['Version 19']]
    parts = axes[1, 1].violinplot(data_for_violin, positions=[1, 2, 3])
    axes[1, 1].set_title('曝光量分布小提琴图')
    axes[1, 1].set_ylabel('曝光量')
    axes[1, 1].set_xticks([1, 2, 3])
    axes[1, 1].set_xticklabels(['Version 15', 'Version 16', 'Version 19'])
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/expo_pv_distribution_analysis.png', dpi=300, bbox_inches='tight')
    print(f"\n可视化图表已保存到: {output_dir}/expo_pv_distribution_analysis.png")
    
    # 2. 区间分布柱状图
    fig, ax = plt.subplots(figsize=(12, 8))
    
    bins = [0, 1, 5, 10, 20, 50, 100, float('inf')]
    labels = ['0', '1-4', '5-9', '10-19', '20-49', '50-99', '100+']
    
    x = np.arange(len(labels))
    width = 0.25
    
    for i, (version_name, data) in enumerate(versions.items()):
        binned = pd.cut(data, bins=bins, labels=labels, right=False)
        counts = binned.value_counts().sort_index()
        percentages = (counts / len(data) * 100)
        
        ax.bar(x + i * width, percentages, width, label=version_name, alpha=0.8)
    
    ax.set_xlabel('曝光量区间')
    ax.set_ylabel('百分比 (%)')
    ax.set_title('不同版本曝光量区间分布比较')
    ax.set_xticks(x + width)
    ax.set_xticklabels(labels)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/expo_pv_interval_distribution.png', dpi=300, bbox_inches='tight')
    print(f"区间分布图已保存到: {output_dir}/expo_pv_interval_distribution.png")

def main():
    parser = argparse.ArgumentParser(description='分析CSV文件中的曝光量分布')
    parser.add_argument('--csv', default='csv/top2k_ctr_diff_15_16_19.csv', 
                       help='CSV文件路径 (默认: csv/top2k_ctr_diff_15_16_19.csv)')
    parser.add_argument('--output', default='./', 
                       help='输出目录 (默认: 当前目录)')
    parser.add_argument('--no-plot', action='store_true', 
                       help='不生成图表')
    
    args = parser.parse_args()
    
    # 加载数据
    df = load_data(args.csv)
    if df is None:
        return
    
    # 分析分布
    versions = analyze_expo_pv_distribution(df)
    
    # 生成可视化图表
    if not args.no_plot:
        try:
            create_visualizations(versions, args.output)
        except Exception as e:
            print(f"生成图表时出错: {e}")
            print("可能需要安装matplotlib: pip install matplotlib")

if __name__ == "__main__":
    main()
