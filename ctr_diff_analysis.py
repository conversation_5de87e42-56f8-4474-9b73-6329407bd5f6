#!/usr/bin/env python3
"""
分析三个版本曝光量都超过200的数据中，有效点击率差值的分布
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

def create_histogram(diff_16_15, diff_19_15):
    """创建CTR差值直方图"""

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 筛选(-0.1, 0.1)范围内的数据
    diff_16_15_filtered = diff_16_15[(diff_16_15 > -0.1) & (diff_16_15 < 0.1)]
    diff_19_15_filtered = diff_19_15[(diff_19_15 > -0.1) & (diff_19_15 < 0.1)]

    print(f"\n" + "=" * 80)
    print("直方图数据范围: (-0.1, 0.1)")
    print("=" * 80)
    print(f"16相比15 在范围内的数据: {len(diff_16_15_filtered)} / {len(diff_16_15)} ({len(diff_16_15_filtered)/len(diff_16_15)*100:.1f}%)")
    print(f"19相比15 在范围内的数据: {len(diff_19_15_filtered)} / {len(diff_19_15)} ({len(diff_19_15_filtered)/len(diff_19_15)*100:.1f}%)")

    # 创建图形
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # 设置bins
    bins = np.linspace(-0.1, 0.1, 21)  # 20个区间

    # 绘制16相比15的直方图
    ax1.hist(diff_16_15_filtered, bins=bins, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_title('16相比15的CTR差值分布\n(曝光量>200)', fontsize=14)
    ax1.set_xlabel('CTR差值', fontsize=12)
    ax1.set_ylabel('频次', fontsize=12)
    ax1.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='零线')
    ax1.axvline(x=diff_16_15_filtered.mean(), color='orange', linestyle='-', alpha=0.8,
                label=f'均值: {diff_16_15_filtered.mean():.4f}')
    ax1.grid(True, alpha=0.3)
    ax1.legend()

    # 绘制19相比15的直方图
    ax2.hist(diff_19_15_filtered, bins=bins, alpha=0.7, color='lightgreen', edgecolor='black')
    ax2.set_title('19相比15的CTR差值分布\n(曝光量>200)', fontsize=14)
    ax2.set_xlabel('CTR差值', fontsize=12)
    ax2.set_ylabel('频次', fontsize=12)
    ax2.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='零线')
    ax2.axvline(x=diff_19_15_filtered.mean(), color='orange', linestyle='-', alpha=0.8,
                label=f'均值: {diff_19_15_filtered.mean():.4f}')
    ax2.grid(True, alpha=0.3)
    ax2.legend()

    plt.tight_layout()
    plt.savefig('ctr_diff_histogram.png', dpi=300, bbox_inches='tight')
    print(f"\n直方图已保存到: ctr_diff_histogram.png")

    # 创建对比直方图
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))

    ax.hist(diff_16_15_filtered, bins=bins, alpha=0.6, color='skyblue',
            label=f'16相比15 (n={len(diff_16_15_filtered)})', edgecolor='black')
    ax.hist(diff_19_15_filtered, bins=bins, alpha=0.6, color='lightgreen',
            label=f'19相比15 (n={len(diff_19_15_filtered)})', edgecolor='black')

    ax.set_title('CTR差值分布对比 (曝光量>200)', fontsize=16)
    ax.set_xlabel('CTR差值', fontsize=14)
    ax.set_ylabel('频次', fontsize=14)
    ax.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='零线')
    ax.axvline(x=diff_16_15_filtered.mean(), color='blue', linestyle='-', alpha=0.8,
               label=f'16相比15均值: {diff_16_15_filtered.mean():.4f}')
    ax.axvline(x=diff_19_15_filtered.mean(), color='green', linestyle='-', alpha=0.8,
               label=f'19相比15均值: {diff_19_15_filtered.mean():.4f}')
    ax.grid(True, alpha=0.3)
    ax.legend()

    plt.tight_layout()
    plt.savefig('ctr_diff_comparison_histogram.png', dpi=300, bbox_inches='tight')
    print(f"对比直方图已保存到: ctr_diff_comparison_histogram.png")

    plt.show()

def analyze_ctr_diff():
    csv_file = 'csv/top2k_ctr_diff_15_16_19.csv'
    
    print("=" * 80)
    print("有效点击率差值分布分析")
    print("筛选条件: 三个版本曝光量都超过200")
    print("=" * 80)
    
    # 读取数据
    df = pd.read_csv(csv_file, na_values=['\\N', 'NULL', 'null', '', 'NaN'])
    print(f"原始数据行数: {len(df)}")
    
    # 转换曝光量列为数值类型
    expo_cols = ['expo_pv_15', 'expo_pv_16', 'expo_pv_19']
    for col in expo_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 转换点击率差值列为数值类型
    diff_cols = ['diff_ctr_eff_16_15', 'diff_ctr_eff_19_15']
    for col in diff_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 筛选三个版本曝光量都超过200的数据
    condition = (df['expo_pv_15'] > 200) & (df['expo_pv_16'] > 200) & (df['expo_pv_19'] > 200)
    filtered_df = df[condition].copy()
    
    print(f"筛选后数据行数: {len(filtered_df)}")
    print(f"筛选比例: {len(filtered_df)/len(df)*100:.2f}%")
    
    if len(filtered_df) == 0:
        print("没有满足条件的数据！")
        return
    
    print(f"\n筛选后数据的曝光量统计:")
    for col in expo_cols:
        data = filtered_df[col].dropna()
        version = col.split('_')[-1]
        print(f"  Version {version}: 均值={data.mean():.1f}, 中位数={data.median():.1f}, 最小值={data.min()}, 最大值={data.max()}")
    
    # 分析点击率差值分布
    print("\n" + "=" * 80)
    print("有效点击率差值分布统计")
    print("=" * 80)
    
    # 16相比15的差值分析
    diff_16_15 = filtered_df['diff_ctr_eff_16_15'].dropna()
    print(f"\n16相比15的点击率差值 (diff_ctr_eff_16_15):")
    print(f"  有效数据: {len(diff_16_15)} 条")
    
    if len(diff_16_15) > 0:
        print(f"  均值: {diff_16_15.mean():.4f}")
        print(f"  中位数: {diff_16_15.median():.4f}")
        print(f"  标准差: {diff_16_15.std():.4f}")
        print(f"  最小值: {diff_16_15.min():.4f}")
        print(f"  最大值: {diff_16_15.max():.4f}")
        print(f"  25%分位: {diff_16_15.quantile(0.25):.4f}")
        print(f"  75%分位: {diff_16_15.quantile(0.75):.4f}")
        
        # 区间分布
        print(f"  区间分布:")
        print(f"    < -0.1: {(diff_16_15 < -0.1).sum()} 条 ({(diff_16_15 < -0.1).sum()/len(diff_16_15)*100:.1f}%)")
        print(f"    -0.1 ~ -0.05: {((diff_16_15 >= -0.1) & (diff_16_15 < -0.05)).sum()} 条 ({((diff_16_15 >= -0.1) & (diff_16_15 < -0.05)).sum()/len(diff_16_15)*100:.1f}%)")
        print(f"    -0.05 ~ 0: {((diff_16_15 >= -0.05) & (diff_16_15 < 0)).sum()} 条 ({((diff_16_15 >= -0.05) & (diff_16_15 < 0)).sum()/len(diff_16_15)*100:.1f}%)")
        print(f"    0 ~ 0.05: {((diff_16_15 >= 0) & (diff_16_15 < 0.05)).sum()} 条 ({((diff_16_15 >= 0) & (diff_16_15 < 0.05)).sum()/len(diff_16_15)*100:.1f}%)")
        print(f"    0.05 ~ 0.1: {((diff_16_15 >= 0.05) & (diff_16_15 < 0.1)).sum()} 条 ({((diff_16_15 >= 0.05) & (diff_16_15 < 0.1)).sum()/len(diff_16_15)*100:.1f}%)")
        print(f"    >= 0.1: {(diff_16_15 >= 0.1).sum()} 条 ({(diff_16_15 >= 0.1).sum()/len(diff_16_15)*100:.1f}%)")
        
        # 正负分布
        positive = (diff_16_15 > 0).sum()
        negative = (diff_16_15 < 0).sum()
        zero = (diff_16_15 == 0).sum()
        print(f"  正值(提升): {positive} 条 ({positive/len(diff_16_15)*100:.1f}%)")
        print(f"  负值(下降): {negative} 条 ({negative/len(diff_16_15)*100:.1f}%)")
        print(f"  零值(无变化): {zero} 条 ({zero/len(diff_16_15)*100:.1f}%)")
    
    # 19相比15的差值分析
    diff_19_15 = filtered_df['diff_ctr_eff_19_15'].dropna()
    print(f"\n19相比15的点击率差值 (diff_ctr_eff_19_15):")
    print(f"  有效数据: {len(diff_19_15)} 条")
    
    if len(diff_19_15) > 0:
        print(f"  均值: {diff_19_15.mean():.4f}")
        print(f"  中位数: {diff_19_15.median():.4f}")
        print(f"  标准差: {diff_19_15.std():.4f}")
        print(f"  最小值: {diff_19_15.min():.4f}")
        print(f"  最大值: {diff_19_15.max():.4f}")
        print(f"  25%分位: {diff_19_15.quantile(0.25):.4f}")
        print(f"  75%分位: {diff_19_15.quantile(0.75):.4f}")
        
        # 区间分布
        print(f"  区间分布:")
        print(f"    < -0.1: {(diff_19_15 < -0.1).sum()} 条 ({(diff_19_15 < -0.1).sum()/len(diff_19_15)*100:.1f}%)")
        print(f"    -0.1 ~ -0.05: {((diff_19_15 >= -0.1) & (diff_19_15 < -0.05)).sum()} 条 ({((diff_19_15 >= -0.1) & (diff_19_15 < -0.05)).sum()/len(diff_19_15)*100:.1f}%)")
        print(f"    -0.05 ~ 0: {((diff_19_15 >= -0.05) & (diff_19_15 < 0)).sum()} 条 ({((diff_19_15 >= -0.05) & (diff_19_15 < 0)).sum()/len(diff_19_15)*100:.1f}%)")
        print(f"    0 ~ 0.05: {((diff_19_15 >= 0) & (diff_19_15 < 0.05)).sum()} 条 ({((diff_19_15 >= 0) & (diff_19_15 < 0.05)).sum()/len(diff_19_15)*100:.1f}%)")
        print(f"    0.05 ~ 0.1: {((diff_19_15 >= 0.05) & (diff_19_15 < 0.1)).sum()} 条 ({((diff_19_15 >= 0.05) & (diff_19_15 < 0.1)).sum()/len(diff_19_15)*100:.1f}%)")
        print(f"    >= 0.1: {(diff_19_15 >= 0.1).sum()} 条 ({(diff_19_15 >= 0.1).sum()/len(diff_19_15)*100:.1f}%)")
        
        # 正负分布
        positive = (diff_19_15 > 0).sum()
        negative = (diff_19_15 < 0).sum()
        zero = (diff_19_15 == 0).sum()
        print(f"  正值(提升): {positive} 条 ({positive/len(diff_19_15)*100:.1f}%)")
        print(f"  负值(下降): {negative} 条 ({negative/len(diff_19_15)*100:.1f}%)")
        print(f"  零值(无变化): {zero} 条 ({zero/len(diff_19_15)*100:.1f}%)")
    
    # 对比分析
    if len(diff_16_15) > 0 and len(diff_19_15) > 0:
        print(f"\n" + "=" * 80)
        print("对比分析")
        print("=" * 80)
        print(f"16相比15 vs 19相比15:")
        print(f"  均值: {diff_16_15.mean():.4f} vs {diff_19_15.mean():.4f}")
        print(f"  中位数: {diff_16_15.median():.4f} vs {diff_19_15.median():.4f}")
        print(f"  提升比例: {(diff_16_15 > 0).sum()/len(diff_16_15)*100:.1f}% vs {(diff_19_15 > 0).sum()/len(diff_19_15)*100:.1f}%")
        print(f"  下降比例: {(diff_16_15 < 0).sum()/len(diff_16_15)*100:.1f}% vs {(diff_19_15 < 0).sum()/len(diff_19_15)*100:.1f}%")
    
    # 显示一些具体的数据样例
    print(f"\n" + "=" * 80)
    print("数据样例 (前10条)")
    print("=" * 80)
    sample_cols = ['content_id', 'expo_pv_15', 'expo_pv_16', 'expo_pv_19', 'diff_ctr_eff_16_15', 'diff_ctr_eff_19_15']
    print(filtered_df[sample_cols].head(10).to_string(index=False))

    # 绘制直方图
    create_histogram(diff_16_15, diff_19_15)

if __name__ == "__main__":
    analyze_ctr_diff()
