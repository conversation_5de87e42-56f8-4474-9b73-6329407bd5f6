#!/usr/bin/env python3
"""
简化版曝光量分布分析脚本 - 只需要pandas
"""

import pandas as pd
import sys

def analyze_expo_distribution(csv_file):
    """分析曝光量分布"""
    
    try:
        # 加载数据
        df = pd.read_csv(csv_file)
        print(f"成功加载数据，共 {len(df)} 行")
        print(f"列名: {list(df.columns)}")
        print()
        
    except Exception as e:
        print(f"加载数据失败: {e}")
        return
    
    # 提取三个版本的曝光量数据
    expo_columns = ['expo_pv_15', 'expo_pv_16', 'expo_pv_19']
    
    print("=" * 80)
    print("曝光量分布统计报告")
    print("=" * 80)
    
    # 基本统计信息
    for col in expo_columns:
        if col in df.columns:
            data = df[col].dropna()
            version = col.split('_')[-1]
            
            print(f"\nVersion {version} 曝光量统计:")
            print(f"  数据量: {len(data):,}")
            print(f"  均值: {data.mean():.2f}")
            print(f"  中位数: {data.median():.2f}")
            print(f"  标准差: {data.std():.2f}")
            print(f"  最小值: {data.min()}")
            print(f"  最大值: {data.max()}")
            print(f"  25%分位数: {data.quantile(0.25):.2f}")
            print(f"  75%分位数: {data.quantile(0.75):.2f}")
            print(f"  95%分位数: {data.quantile(0.95):.2f}")
            print(f"  99%分位数: {data.quantile(0.99):.2f}")
    
    # 区间分布统计
    print("\n" + "=" * 80)
    print("曝光量区间分布")
    print("=" * 80)
    
    # 定义区间
    bins = [0, 1, 2, 5, 10, 20, 50, 100, 200, 500, float('inf')]
    labels = ['0', '1', '2-4', '5-9', '10-19', '20-49', '50-99', '100-199', '200-499', '500+']
    
    for col in expo_columns:
        if col in df.columns:
            data = df[col].dropna()
            version = col.split('_')[-1]
            
            print(f"\nVersion {version} 区间分布:")
            binned = pd.cut(data, bins=bins, labels=labels, right=False)
            counts = binned.value_counts().sort_index()
            
            print(f"{'区间':>10} {'数量':>8} {'百分比':>8} {'累积%':>8}")
            print("-" * 36)
            
            cumulative = 0
            for label in labels:
                count = counts.get(label, 0)
                percentage = (count / len(data) * 100)
                cumulative += percentage
                print(f"{label:>10} {count:>8} {percentage:>7.2f}% {cumulative:>7.2f}%")
    
    # 最常见的数值
    print("\n" + "=" * 80)
    print("最常见的曝光量数值 (前15个)")
    print("=" * 80)
    
    for col in expo_columns:
        if col in df.columns:
            data = df[col].dropna()
            version = col.split('_')[-1]
            
            print(f"\nVersion {version} 最常见数值:")
            value_counts = data.value_counts().head(15)
            
            print(f"{'数值':>6} {'数量':>8} {'百分比':>8}")
            print("-" * 24)
            
            for value, count in value_counts.items():
                percentage = (count / len(data) * 100)
                print(f"{value:>6} {count:>8} {percentage:>7.2f}%")
    
    # 版本间比较
    print("\n" + "=" * 80)
    print("版本间比较")
    print("=" * 80)
    
    # 创建比较表
    comparison_data = []
    for col in expo_columns:
        if col in df.columns:
            data = df[col].dropna()
            version = col.split('_')[-1]
            comparison_data.append({
                'Version': version,
                'Count': len(data),
                'Mean': data.mean(),
                'Median': data.median(),
                'Std': data.std(),
                'Min': data.min(),
                'Max': data.max(),
                'Q25': data.quantile(0.25),
                'Q75': data.quantile(0.75)
            })
    
    if comparison_data:
        comparison_df = pd.DataFrame(comparison_data)
        print("\n统计指标对比:")
        print(comparison_df.round(2).to_string(index=False))
    
    # 零曝光量统计
    print("\n" + "=" * 80)
    print("零曝光量统计")
    print("=" * 80)
    
    for col in expo_columns:
        if col in df.columns:
            data = df[col].dropna()
            version = col.split('_')[-1]
            zero_count = (data == 0).sum()
            zero_percentage = (zero_count / len(data) * 100)
            print(f"Version {version}: {zero_count:,} 条零曝光 ({zero_percentage:.2f}%)")
    
    # 高曝光量统计 (>100)
    print("\n高曝光量统计 (>100):")
    for col in expo_columns:
        if col in df.columns:
            data = df[col].dropna()
            version = col.split('_')[-1]
            high_count = (data > 100).sum()
            high_percentage = (high_count / len(data) * 100)
            if high_count > 0:
                high_mean = data[data > 100].mean()
                print(f"Version {version}: {high_count:,} 条高曝光 ({high_percentage:.2f}%), 平均值: {high_mean:.2f}")
            else:
                print(f"Version {version}: {high_count:,} 条高曝光 ({high_percentage:.2f}%)")

def main():
    csv_file = 'csv/top2k_ctr_diff_15_16_19.csv'
    
    if len(sys.argv) > 1:
        csv_file = sys.argv[1]
    
    print(f"分析文件: {csv_file}")
    analyze_expo_distribution(csv_file)

if __name__ == "__main__":
    main()
