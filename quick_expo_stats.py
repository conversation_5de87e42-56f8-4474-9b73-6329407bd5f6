#!/usr/bin/env python3
"""
快速曝光量统计脚本
"""

import pandas as pd

def quick_stats():
    csv_file = 'csv/top2k_ctr_diff_15_16_19.csv'
    
    print("=" * 60)
    print("曝光量分布快速统计")
    print("=" * 60)
    
    # 读取数据
    df = pd.read_csv(csv_file, na_values=['\\N', 'NULL', 'null', '', 'NaN'])
    print(f"数据行数: {len(df)}")
    
    # 处理曝光量列
    expo_cols = ['expo_pv_15', 'expo_pv_16', 'expo_pv_19']
    
    for col in expo_cols:
        if col in df.columns:
            # 转换为数值
            data = pd.to_numeric(df[col], errors='coerce').dropna()
            version = col.split('_')[-1]
            
            print(f"\nVersion {version} 统计:")
            print(f"  有效数据: {len(data):,} 条")
            
            if len(data) > 0:
                print(f"  均值: {data.mean():.2f}")
                print(f"  中位数: {data.median():.2f}")
                print(f"  标准差: {data.std():.2f}")
                print(f"  最小值: {data.min()}")
                print(f"  最大值: {data.max()}")
                print(f"  25%分位: {data.quantile(0.25):.2f}")
                print(f"  75%分位: {data.quantile(0.75):.2f}")
                
                # 区间统计
                print(f"  零曝光: {(data == 0).sum():,} 条 ({(data == 0).sum()/len(data)*100:.1f}%)")
                print(f"  1-10: {((data >= 1) & (data <= 10)).sum():,} 条")
                print(f"  11-50: {((data >= 11) & (data <= 50)).sum():,} 条")
                print(f"  51-100: {((data >= 51) & (data <= 100)).sum():,} 条")
                print(f"  101-200: {((data >= 101) & (data <= 200)).sum():,} 条")
                print(f"  201-500: {((data >= 201) & (data <= 500)).sum():,} 条")
                print(f"  500+: {(data > 500).sum():,} 条")
                
                # 最常见的值
                top_values = data.value_counts().head(5)
                print(f"  最常见值:")
                for val, count in top_values.items():
                    print(f"    {val}: {count} 次 ({count/len(data)*100:.1f}%)")
            else:
                print("  无有效数据")

if __name__ == "__main__":
    quick_stats()
