#!/usr/bin/env python3
"""
脚本用于下载CTR差值对比图
- 筛选条件：曝光量>200，CTR差值绝对值>0.01
- 分别下载16vs15和19vs15的正负向结果对比
- 正向：CTR差值>0.01（提升）
- 负向：CTR差值<-0.01（下降）
"""

import os
import sys
from pathlib import Path
import pandas as pd
from afts import Afts
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO
import time
from tqdm import tqdm

# AFTS配置
endpoint_config = {
    "upload_endpoint_source": "mass.alipay.com",
    "download_endpoint_source": "mdn.alipayobjects.com",
    "authority_endpoint": "mmtcapi.alipay.com"
}
biz_key = "content_liveface"
biz_secret = "31cedb0f8bc24b778af865d8c5704705"

# 初始化AFTS客户端
_afts = Afts(
    biz_key=biz_key,
    biz_secret=biz_secret,
    endpoint_config=endpoint_config
)

def download_image_from_afts(afts_id):
    """从AFTS下载图片并返回PIL Image对象"""
    try:
        content = _afts.download_file(afts_id)
        image = Image.open(BytesIO(content))
        return image
    except Exception as e:
        print(f"下载图片失败 {afts_id}: {e}")
        return None

def add_ctr_text_to_image(image, ctr_value, version_info, is_baseline=False):
    """
    在图片上添加CTR值和版本信息

    Args:
        image (PIL.Image): 图片对象
        ctr_value (float): CTR值
        version_info (str): 版本信息（如"Version 15"）
        is_baseline (bool): 是否为基准版本

    Returns:
        PIL.Image: 添加文本后的图片
    """
    img_with_text = image.copy()
    draw = ImageDraw.Draw(img_with_text)

    # 格式化CTR值
    ctr_text = f"CTR: {ctr_value:.3f}"

    # 文本颜色
    if is_baseline:
        text_color = (255, 255, 255)  # 基准版本用白色
    else:
        text_color = (255, 255, 0)  # 对比版本用黄色

    # 完整文本
    full_text = f"{version_info}\n{ctr_text}"

    # 字体设置
    try:
        font_size = max(16, min(image.width // 20, image.height // 20))
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
    except:
        try:
            font = ImageFont.load_default()
        except:
            font = None

    # 文本位置
    margin = 10
    text_x = margin
    text_y = margin

    # 添加背景矩形
    lines = full_text.split('\n')
    max_width = 0
    total_height = 0

    for line in lines:
        if font:
            bbox = draw.textbbox((0, 0), line, font=font)
        else:
            bbox = draw.textbbox((0, 0), line)
        line_width = bbox[2] - bbox[0]
        line_height = bbox[3] - bbox[1]
        max_width = max(max_width, line_width)
        total_height += line_height + 2

    # 绘制半透明背景
    background_color = (0, 0, 0, 180)
    draw.rectangle([text_x-5, text_y-2, text_x+max_width+10, text_y+total_height+5],
                   fill=background_color)

    # 绘制文本
    current_y = text_y
    for i, line in enumerate(lines):
        color = (255, 255, 255) if i == 0 else text_color  # 版本信息用白色，CTR值用指定颜色
        if font:
            draw.text((text_x, current_y), line, fill=color, font=font)
            bbox = draw.textbbox((0, 0), line, font=font)
        else:
            draw.text((text_x, current_y), line, fill=color)
            bbox = draw.textbbox((0, 0), line)
        current_y += bbox[3] - bbox[1] + 2

    return img_with_text

def create_ctr_comparison_image(image_15, image_other, ctr_15, ctr_other, version_info):
    """
    创建CTR对比图：左边版本15，右边其他版本

    Args:
        image_15 (PIL.Image): 版本15图片
        image_other (PIL.Image): 其他版本图片
        ctr_15 (float): 版本15的CTR值
        ctr_other (float): 其他版本的CTR值
        version_info (str): 版本信息

    Returns:
        PIL.Image: 拼接后的对比图
    """
    # 确保两张图片高度一致
    target_height = min(image_15.height, image_other.height)

    # 按比例调整图片大小
    ratio_15 = target_height / image_15.height
    ratio_other = target_height / image_other.height

    new_width_15 = int(image_15.width * ratio_15)
    new_width_other = int(image_other.width * ratio_other)

    image_15_resized = image_15.resize((new_width_15, target_height), Image.Resampling.LANCZOS)
    image_other_resized = image_other.resize((new_width_other, target_height), Image.Resampling.LANCZOS)

    # 在图片上添加标注
    image_15_with_text = add_ctr_text_to_image(image_15_resized, ctr_15, "Version 15", is_baseline=True)
    image_other_with_text = add_ctr_text_to_image(image_other_resized, ctr_other, version_info, is_baseline=False)

    # 创建拼接图片
    total_width = new_width_15 + new_width_other
    comparison_image = Image.new('RGB', (total_width, target_height), (255, 255, 255))

    # 粘贴两张图片
    comparison_image.paste(image_15_with_text, (0, 0))
    comparison_image.paste(image_other_with_text, (new_width_15, 0))

    return comparison_image

def filter_data_by_criteria(df):
    """
    根据条件筛选数据
    - 三个版本曝光量都>200
    - CTR差值绝对值>0.01
    """
    # 转换数值列
    numeric_cols = ['expo_pv_15', 'expo_pv_16', 'expo_pv_19', 'diff_ctr_eff_16_15', 'diff_ctr_eff_19_15']
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 筛选条件
    expo_condition = (df['expo_pv_15'] > 200) & (df['expo_pv_16'] > 200) & (df['expo_pv_19'] > 200)
    
    # 16vs15的正负向数据
    diff_16_15_positive = expo_condition & (df['diff_ctr_eff_16_15'] > 0.01)
    diff_16_15_negative = expo_condition & (df['diff_ctr_eff_16_15'] < -0.01)
    
    # 19vs15的正负向数据
    diff_19_15_positive = expo_condition & (df['diff_ctr_eff_19_15'] > 0.01)
    diff_19_15_negative = expo_condition & (df['diff_ctr_eff_19_15'] < -0.01)
    
    return {
        '16vs15_positive': df[diff_16_15_positive].copy(),
        '16vs15_negative': df[diff_16_15_negative].copy(),
        '19vs15_positive': df[diff_19_15_positive].copy(),
        '19vs15_negative': df[diff_19_15_negative].copy()
    }

def create_output_dir(base_dir, category):
    """创建输出目录"""
    output_dir = Path(base_dir) / category
    output_dir.mkdir(parents=True, exist_ok=True)
    return output_dir

def download_ctr_diff_comparisons(csv_file, output_base_dir="ctr_diff_downloads", max_per_category=20):
    """
    下载CTR差值对比图

    Args:
        csv_file (str): CSV文件路径
        output_base_dir (str): 输出基础目录
        max_per_category (int): 每个类别最大下载数量
    """
    print(f"开始从 {csv_file} 下载CTR差值对比图...")
    print(f"筛选条件: 曝光量>200, |CTR差值|>0.01")
    print(f"文件保存基础目录: {output_base_dir}")

    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file, na_values=['\\N', 'NULL', 'null', '', 'NaN'])
        print(f"原始数据行数: {len(df)}")

        # 筛选数据
        filtered_data = filter_data_by_criteria(df)

        # 打印筛选结果统计
        print(f"\n=== 筛选结果统计 ===")
        for category, data in filtered_data.items():
            print(f"{category}: {len(data)} 条")

        # 处理每个类别
        total_successful = 0
        total_failed = 0

        for category, data in filtered_data.items():
            if len(data) == 0:
                print(f"\n{category}: 无数据，跳过")
                continue

            print(f"\n=== 处理 {category} ===")

            # 限制下载数量
            if len(data) > max_per_category:
                data = data.head(max_per_category)
                print(f"限制下载数量为 {max_per_category} 条")

            # 创建输出目录
            output_dir = create_output_dir(output_base_dir, category)

            # 确定版本信息和相关列
            if '16vs15' in category:
                version_info = "Version 16"
                cover_15_col = 'cover_15'
                cover_other_col = 'cover_16'
                diff_col = 'diff_ctr_eff_16_15'
                ctr_15_col = 'ctr_eff_15'
                ctr_other_col = 'ctr_eff_16'
            else:  # 19vs15
                version_info = "Version 19"
                cover_15_col = 'cover_15'
                cover_other_col = 'cover_19'
                diff_col = 'diff_ctr_eff_19_15'
                ctr_15_col = 'ctr_eff_15'
                ctr_other_col = 'ctr_eff_19'

            successful = 0
            failed = 0

            # 使用tqdm显示进度
            with tqdm(total=len(data), desc=f"下载{category}", unit="张") as pbar:
                for _, row in data.iterrows():
                    content_id = row['content_id']
                    cover_15_id = row[cover_15_col]
                    cover_other_id = row[cover_other_col]
                    ctr_diff = row[diff_col]
                    ctr_15 = row[ctr_15_col]
                    ctr_other = row[ctr_other_col]

                    pbar.set_description(f"处理 {content_id[:15]}...")

                    try:
                        # 下载两张图片
                        image_15 = download_image_from_afts(cover_15_id)
                        image_other = download_image_from_afts(cover_other_id)

                        if image_15 is None or image_other is None:
                            failed += 1
                            pbar.update(1)
                            continue

                        # 创建对比图
                        comparison_image = create_ctr_comparison_image(
                            image_15, image_other, ctr_15, ctr_other, version_info
                        )

                        # 保存图片 - diff值放在最前面方便排序
                        direction = "pos" if ctr_diff > 0 else "neg"
                        output_filename = f"diff_{abs(ctr_diff):.3f}_{direction}_{content_id}.jpg"
                        output_path = output_dir / output_filename
                        comparison_image.save(output_path, 'JPEG', quality=95)

                        successful += 1

                    except Exception as e:
                        print(f"处理失败 {content_id}: {e}")
                        failed += 1

                    pbar.update(1)
                    time.sleep(0.1)  # 避免请求过于频繁

            print(f"{category}: 成功 {successful}, 失败 {failed}")
            total_successful += successful
            total_failed += failed

        print(f"\n=== 总体统计 ===")
        print(f"总成功: {total_successful}")
        print(f"总失败: {total_failed}")

    except FileNotFoundError:
        print(f"错误: 找不到CSV文件 {csv_file}")
        return
    except Exception as e:
        print(f"处理CSV文件时出错: {e}")
        return

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='下载CTR差值对比图')
    parser.add_argument('--csv', default='csv/top2k_ctr_diff_15_16_19.csv',
                       help='CSV文件路径 (默认: csv/top2k_ctr_diff_15_16_19.csv)')
    parser.add_argument('-o', '--output', default='ctr_diff_downloads',
                       help='输出基础目录 (默认: ctr_diff_downloads)')
    parser.add_argument('-n', '--max-per-category', type=int, default=200,
                       help='每个类别最大下载数量 (默认: 200)')

    args = parser.parse_args()

    # 检查CSV文件是否存在
    if not os.path.exists(args.csv):
        print(f"错误: CSV文件不存在: {args.csv}")
        sys.exit(1)

    # 开始下载
    download_ctr_diff_comparisons(args.csv, args.output, args.max_per_category)

if __name__ == "__main__":
    main()
