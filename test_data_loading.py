#!/usr/bin/env python3
"""
测试数据加载和处理
"""

import pandas as pd

def test_data_loading():
    csv_file = 'csv/top2k_ctr_diff_15_16_19.csv'
    
    print("测试数据加载...")
    
    try:
        # 读取CSV时处理空值标记
        df = pd.read_csv(csv_file, na_values=['\\N', 'NULL', 'null', '', 'NaN'])
        print(f"成功加载数据，共 {len(df)} 行")
        print(f"列名: {list(df.columns)}")
        
        # 检查曝光量列
        expo_columns = ['expo_pv_15', 'expo_pv_16', 'expo_pv_19']
        
        print("\n原始数据类型和空值情况:")
        for col in expo_columns:
            if col in df.columns:
                print(f"{col}:")
                print(f"  数据类型: {df[col].dtype}")
                print(f"  总数: {len(df[col])}")
                print(f"  非空: {df[col].notna().sum()}")
                print(f"  空值: {df[col].isna().sum()}")
                print(f"  前5个值: {df[col].head().tolist()}")
        
        # 转换为数值类型
        print("\n转换为数值类型后:")
        for col in expo_columns:
            if col in df.columns:
                original_notna = df[col].notna().sum()
                df[col] = pd.to_numeric(df[col], errors='coerce')
                new_notna = df[col].notna().sum()
                print(f"{col}:")
                print(f"  转换前非空: {original_notna}")
                print(f"  转换后非空: {new_notna}")
                print(f"  转换失败: {original_notna - new_notna}")
                if new_notna > 0:
                    print(f"  均值: {df[col].mean():.2f}")
                    print(f"  中位数: {df[col].median():.2f}")
                    print(f"  最小值: {df[col].min()}")
                    print(f"  最大值: {df[col].max()}")
        
        print("\n数据加载测试成功！")
        return True
        
    except Exception as e:
        print(f"数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_data_loading()
